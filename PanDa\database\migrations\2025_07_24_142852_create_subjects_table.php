<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('subjects', function (Blueprint $table) {
            $table->id();
            $table->string('name'); // Tên môn học
            $table->text('description')->nullable(); // <PERSON>ô tả môn học
            $table->decimal('original_price', 10, 2)->default(0); // Giá gốc
            $table->decimal('sale_price', 10, 2)->nullable(); // Gi<PERSON> khuyến mãi
            $table->string('image')->nullable(); // Hình ảnh đại diện
            $table->enum('status', ['draft', 'published', 'archived'])->default('draft'); // Trạng thái
            $table->integer('sort_order')->default(0); // Th<PERSON> tự sắp xếp
            $table->foreignId('instructor_id')->nullable()->constrained('users')->onDelete('set null'); // Giảng viên
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('subjects');
    }
};
