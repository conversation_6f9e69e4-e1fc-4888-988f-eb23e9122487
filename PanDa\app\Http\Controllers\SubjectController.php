<?php

namespace App\Http\Controllers;

use App\Models\Subject;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;

class SubjectController extends Controller
{
    /**
     * Hiển <PERSON>ch <PERSON>
     */
    public function index(Request $request)
    {
        $query = Subject::with('instructor')->ordered();

        // Lọc theo trạng thái
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Tìm kiếm theo tên
        if ($request->filled('search')) {
            $query->where('name', 'like', '%' . $request->search . '%');
        }

        $subjects = $query->paginate(10);

        return view('subjects.index', compact('subjects'));
    }

    /**
     * Hiển Thị Form Tạo Môn H<PERSON>
     */
    public function create()
    {
        $instructors = User::orderBy('name')->get();
        return view('subjects.create', compact('instructors'));
    }

    /**
     * <PERSON><PERSON><PERSON>
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'original_price' => 'required|numeric|min:0',
            'sale_price' => 'nullable|numeric|min:0|lt:original_price',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'status' => 'required|in:draft,published,archived',
            'instructor_id' => 'nullable|exists:users,id',
        ], [
            'name.required' => 'Vui lòng nhập tên môn học.',
            'name.max' => 'Tên môn học không được vượt quá 255 ký tự.',
            'original_price.required' => 'Vui lòng nhập giá gốc.',
            'original_price.numeric' => 'Giá gốc phải là số.',
            'original_price.min' => 'Giá gốc không được âm.',
            'sale_price.numeric' => 'Giá khuyến mãi phải là số.',
            'sale_price.min' => 'Giá khuyến mãi không được âm.',
            'sale_price.lt' => 'Giá khuyến mãi phải nhỏ hơn giá gốc.',
            'image.image' => 'File phải là hình ảnh.',
            'image.mimes' => 'Hình ảnh phải có định dạng: jpeg, png, jpg, gif.',
            'image.max' => 'Kích thước hình ảnh không được vượt quá 2MB.',
            'status.required' => 'Vui lòng chọn trạng thái.',
            'status.in' => 'Trạng thái không hợp lệ.',
            'instructor_id.exists' => 'Giảng viên không tồn tại.',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        $data = $request->only(['name', 'description', 'original_price', 'sale_price', 'status', 'instructor_id']);

        // Xử lý upload hình ảnh
        if ($request->hasFile('image')) {
            $data['image'] = $request->file('image')->store('subjects', 'public');
        }

        // Tự động tạo sort_order
        $data['sort_order'] = Subject::max('sort_order') + 1;

        Subject::create($data);

        return redirect()->route('subjects.index')->with('success', 'Tạo môn học thành công!');
    }
