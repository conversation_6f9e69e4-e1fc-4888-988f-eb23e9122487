<!DOCTYPE html>
<html lang="vi" dir="ltr" data-bs-theme="light" data-color-theme="Blue_Theme" data-layout="vertical">

<head>
  <!-- Required meta tags -->
  <meta charset="UTF-8" />
  <meta http-equiv="X-UA-Compatible" content="IE=edge" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />

  <!-- Favicon icon-->
  <link rel="shortcut icon" type="image/png" href="<?php echo e(asset('assets/images/logos/favicon.png')); ?>" />

  <!-- Bootstrap CSS -->
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
  <!-- Font Awesome Icons -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
  <!-- Core Css -->
  <link rel="stylesheet" href="<?php echo e(asset('assets/css/styles.css')); ?>" />

  <title>Đăng Ký - Hệ Thống Quản Lý Khóa Học</title>
</head>

<body>
  <!-- Preloader -->
  <div class="preloader">
    <img src="<?php echo e(asset('assets/images/logos/favicon.png')); ?>" alt="loader" class="lds-ripple img-fluid" />
  </div>
  <div id="main-wrapper">
    <div class="position-relative overflow-hidden auth-bg min-vh-100 w-100 d-flex align-items-center justify-content-center">
      <div class="d-flex align-items-center justify-content-center w-100">
        <div class="row justify-content-center w-100 my-5 my-xl-0">
          <div class="col-md-9 d-flex flex-column justify-content-center">
            <div class="card mb-0 bg-body auth-login m-auto w-100">
              <div class="row gx-0">
                <!-- ------------------------------------------------- -->
                <!-- Phần 1 - Form Đăng Ký -->
                <!-- ------------------------------------------------- -->
                <div class="col-xl-6 border-end">
                  <div class="row justify-content-center py-4">
                    <div class="col-lg-11">
                      <div class="card-body">
                        <a href="<?php echo e(route('home')); ?>" class="text-nowrap logo-img d-block mb-4 w-100">
                          <img src="<?php echo e(asset('assets/images/logos/logo.svg')); ?>" class="dark-logo" alt="Logo" />
                        </a>
                        <h2 class="lh-base mb-4">Tạo Tài Khoản Mới</h2>
                        
                        <?php if($errors->any()): ?>
                            <div class="alert alert-danger">
                                <ul class="mb-0">
                                    <?php $__currentLoopData = $errors->all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $error): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <li><?php echo e($error); ?></li>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </ul>
                            </div>
                        <?php endif; ?>

                        <div class="row">
                          <div class="col-6 mb-2 mb-sm-0">
                            <a class="btn btn-white shadow-sm text-dark link-primary border fw-semibold d-flex align-items-center justify-content-center rounded-1 py-6" href="javascript:void(0)" role="button">
                              <i class="fa fa-facebook me-2" style="color: #1877f2;"></i>
                              <span class="d-none d-xxl-inline-flex">Đăng Ký Với</span>&nbsp; Facebook
                            </a>
                          </div>
                          <div class="col-6">
                            <a class="btn btn-white shadow-sm text-dark link-primary border fw-semibold d-flex align-items-center justify-content-center rounded-1 py-6" href="javascript:void(0)" role="button">
                              <i class="fa fa-google me-2" style="color: #db4437;"></i>
                              <span class="d-none d-xxl-inline-flex">Đăng Ký Với</span>&nbsp; Google
                            </a>
                          </div>
                        </div>
                        <div class="position-relative text-center my-4">
                          <p class="mb-0 fs-12 px-3 d-inline-block bg-body z-index-5 position-relative">Hoặc Đăng Ký Bằng Email</p>
                          <span class="border-top w-100 position-absolute top-50 start-50 translate-middle"></span>
                        </div>
                        
                        <form method="POST" action="<?php echo e(route('register')); ?>">
                            <?php echo csrf_field(); ?>
                          <div class="mb-3">
                            <label for="name" class="form-label">Họ Và Tên</label>
                            <input type="text" class="form-control <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" id="name" name="name" value="<?php echo e(old('name')); ?>" placeholder="Nhập họ và tên của bạn" required autofocus>
                            <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                          </div>
                          <div class="mb-3">
                            <label for="email" class="form-label">Địa Chỉ Email</label>
                            <input type="email" class="form-control <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" id="email" name="email" value="<?php echo e(old('email')); ?>" placeholder="Nhập địa chỉ email của bạn" required>
                            <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                          </div>
                          <div class="mb-3">
                            <label for="password" class="form-label">Mật Khẩu</label>
                            <input type="password" class="form-control <?php $__errorArgs = ['password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" id="password" name="password" placeholder="Nhập mật khẩu của bạn" required>
                            <?php $__errorArgs = ['password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                          </div>
                          <div class="mb-4">
                            <label for="password_confirmation" class="form-label">Xác Nhận Mật Khẩu</label>
                            <input type="password" class="form-control" id="password_confirmation" name="password_confirmation" placeholder="Nhập lại mật khẩu của bạn" required>
                          </div>
                          <div class="d-flex align-items-center justify-content-between mb-4">
                            <div class="form-check">
                              <input class="form-check-input primary" type="checkbox" id="terms" required>
                              <label class="form-check-label text-dark" for="terms">
                                Tôi Đồng Ý Với <a href="#" class="text-primary">Điều Khoản Sử Dụng</a>
                              </label>
                            </div>
                          </div>
                          <button type="submit" class="btn btn-dark w-100 py-8 mb-4 rounded-1">Đăng Ký</button>
                          <div class="d-flex align-items-center">
                            <p class="fs-12 mb-0 fw-medium">Đã Có Tài Khoản?</p>
                            <a class="text-primary fw-bolder ms-2" href="<?php echo e(route('login')); ?>">Đăng Nhập Ngay</a>
                          </div>
                        </form>
                      </div>
                    </div>
                  </div>
                </div>
                <!-- ------------------------------------------------- -->
                <!-- Phần 2 - Hình Ảnh Minh Họa -->
                <!-- ------------------------------------------------- -->
                <div class="col-xl-6 d-none d-xl-block">
                  <div class="row justify-content-center align-items-center h-100">
                    <div class="col-lg-9">
                      <div id="auth-register" class="carousel slide auth-carousel" data-bs-ride="carousel">
                        <div class="carousel-indicators">
                          <button type="button" data-bs-target="#auth-register" data-bs-slide-to="0" class="active" aria-current="true" aria-label="Slide 1"></button>
                          <button type="button" data-bs-target="#auth-register" data-bs-slide-to="1" aria-label="Slide 2"></button>
                          <button type="button" data-bs-target="#auth-register" data-bs-slide-to="2" aria-label="Slide 3"></button>
                        </div>
                        <div class="carousel-inner">
                          <div class="carousel-item active">
                            <div class="d-flex align-items-center justify-content-center w-100 h-100 flex-column gap-9 text-center">
                              <img src="<?php echo e(asset('assets/images/backgrounds/login-side.png')); ?>" alt="register-side-img" width="300" class="img-fluid" />
                              <h4 class="mb-0">Bắt Đầu Hành Trình Học Tập</h4>
                              <p class="fs-12 mb-0">Tham gia cộng đồng học tập trực tuyến với hàng ngàn khóa học chất lượng cao và giảng viên kinh nghiệm.</p>
                              <a href="javascript:void(0)" class="btn btn-primary rounded-1">Khám Phá Khóa Học</a>
                            </div>
                          </div>
                          <div class="carousel-item">
                            <div class="d-flex align-items-center justify-content-center w-100 h-100 flex-column gap-9 text-center">
                              <img src="<?php echo e(asset('assets/images/backgrounds/login-side.png')); ?>" alt="register-side-img" width="300" class="img-fluid" />
                              <h4 class="mb-0">Học Tập Linh Hoạt</h4>
                              <p class="fs-12 mb-0">Học mọi lúc, mọi nơi với nền tảng hỗ trợ đa thiết bị và nội dung đa dạng từ video đến tài liệu.</p>
                              <a href="javascript:void(0)" class="btn btn-primary rounded-1">Tìm Hiểu Thêm</a>
                            </div>
                          </div>
                          <div class="carousel-item">
                            <div class="d-flex align-items-center justify-content-center w-100 h-100 flex-column gap-9 text-center">
                              <img src="<?php echo e(asset('assets/images/backgrounds/login-side.png')); ?>" alt="register-side-img" width="300" class="img-fluid" />
                              <h4 class="mb-0">Chứng Chỉ Uy Tín</h4>
                              <p class="fs-12 mb-0">Nhận chứng chỉ hoàn thành khóa học được công nhận, nâng cao giá trị bản thân trong công việc.</p>
                              <a href="javascript:void(0)" class="btn btn-primary rounded-1">Xem Chứng Chỉ</a>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="dark-transparent sidebartoggler"></div>
  <!-- Import Js Files -->
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
  <script src="<?php echo e(asset('assets/js/theme/app.init.js')); ?>"></script>
  <script src="<?php echo e(asset('assets/js/theme/theme.js')); ?>"></script>
  <script src="<?php echo e(asset('assets/js/theme/app.min.js')); ?>"></script>
</body>

</html>
<?php /**PATH D:\Src Công Việc\PanDa\resources\views/auth/register.blade.php ENDPATH**/ ?>