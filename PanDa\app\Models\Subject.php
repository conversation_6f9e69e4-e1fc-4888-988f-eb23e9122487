<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Subject extends Model
{
    /**
     * Các trường có thể được gán hàng loạt
     */
    protected $fillable = [
        'name',
        'description',
        'original_price',
        'sale_price',
        'image',
        'status',
        'sort_order',
        'instructor_id',
    ];

    /**
     * Các trường được cast
     */
    protected $casts = [
        'original_price' => 'decimal:2',
        'sale_price' => 'decimal:2',
        'sort_order' => 'integer',
    ];

    /**
     * Quan hệ với User (Giảng viên)
     */
    public function instructor(): BelongsTo
    {
        return $this->belongsTo(User::class, 'instructor_id');
    }

    /**
     * Quan hệ với Chapter (Chương)
     */
    public function chapters(): HasMany
    {
        return $this->hasMany(Chapter::class)->orderBy('sort_order');
    }

    /**
     * L<PERSON>y giá hiển thị (ưu tiên giá khuyến mãi)
     */
    public function getDisplayPriceAttribute()
    {
        return $this->sale_price ?? $this->original_price;
    }

    /**
     * Kiểm tra có giá khuyến mãi không
     */
    public function getHasSaleAttribute()
    {
        return !is_null($this->sale_price) && $this->sale_price < $this->original_price;
    }

    /**
     * Lấy trạng thái bằng tiếng Việt
     */
    public function getStatusLabelAttribute()
    {
        return match($this->status) {
            'draft' => 'Nháp',
            'published' => 'Đã Xuất Bản',
            'archived' => 'Lưu Trữ',
            default => 'Không Xác Định'
        };
    }

    /**
     * Scope để lọc theo trạng thái
     */
    public function scopePublished($query)
    {
        return $query->where('status', 'published');
    }

    public function scopeDraft($query)
    {
        return $query->where('status', 'draft');
    }

    public function scopeArchived($query)
    {
        return $query->where('status', 'archived');
    }

    /**
     * Scope để sắp xếp theo thứ tự
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order')->orderBy('created_at');
    }
}
