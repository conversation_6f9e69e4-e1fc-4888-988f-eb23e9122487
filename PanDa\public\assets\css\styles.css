/* Styles cơ bản cho hệ thống xác thực */

/* Preloader */
.preloader {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: #fff;
    z-index: 9999;
    display: flex;
    align-items: center;
    justify-content: center;
}

.lds-ripple {
    width: 80px;
    height: 80px;
}

/* Auth Background */
.auth-bg {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
}

/* Auth Card */
.auth-login {
    max-width: 900px;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

/* Form Styles */
.form-control {
    border-radius: 8px;
    border: 1px solid #e0e0e0;
    padding: 12px 15px;
    font-size: 14px;
}

.form-control:focus {
    border-color: #5d87ff;
    box-shadow: 0 0 0 0.2rem rgba(93, 135, 255, 0.25);
}

.btn {
    border-radius: 8px;
    font-weight: 500;
    padding: 12px 24px;
}

.btn-dark {
    background-color: #2a3547;
    border-color: #2a3547;
}

.btn-dark:hover {
    background-color: #1e2832;
    border-color: #1e2832;
}

/* Carousel */
.auth-carousel .carousel-item {
    min-height: 400px;
}

/* Logo */
.logo-img img {
    max-height: 40px;
}

/* Sidebar Styles */
.left-sidebar {
    width: 270px;
    position: fixed;
    top: 0;
    left: 0;
    height: 100vh;
    background: #fff;
    box-shadow: 0 0 20px rgba(0,0,0,0.1);
    z-index: 1000;
}

.sidebar-nav {
    height: calc(100vh - 80px);
    overflow-y: auto;
}

.sidebar-item {
    list-style: none;
}

.sidebar-link {
    display: flex;
    align-items: center;
    padding: 12px 20px;
    color: #5a6a85;
    text-decoration: none;
    transition: all 0.3s ease;
}

.sidebar-link:hover {
    background-color: #f8f9fa;
    color: #5d87ff;
}

.sidebar-link i {
    width: 20px;
    margin-right: 10px;
}

/* Header */
.topbar {
    height: 70px;
    background: #fff;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    position: fixed;
    top: 0;
    left: 270px;
    right: 0;
    z-index: 999;
}

/* Page Wrapper */
.page-wrapper {
    margin-left: 270px;
    margin-top: 70px;
    min-height: calc(100vh - 70px);
}

.body-wrapper {
    padding: 20px;
}

/* Responsive */
@media (max-width: 1199px) {
    .left-sidebar {
        transform: translateX(-100%);
    }
    
    .page-wrapper {
        margin-left: 0;
    }
    
    .topbar {
        left: 0;
    }
}

/* Utility Classes */
.fs-12 { font-size: 12px; }
.fs-2 { font-size: 14px; }
.fs-3 { font-size: 16px; }
.fs-4 { font-size: 18px; }
.fs-5 { font-size: 20px; }
.fs-6 { font-size: 24px; }

.fw-semibold { font-weight: 600; }
.fw-bolder { font-weight: 700; }

.text-primary { color: #5d87ff !important; }
.text-muted { color: #6c757d !important; }

.bg-primary-subtle { background-color: rgba(93, 135, 255, 0.1) !important; }

.rounded-1 { border-radius: 0.25rem !important; }

.py-6 { padding-top: 1.5rem !important; padding-bottom: 1.5rem !important; }
.py-8 { padding-top: 2rem !important; padding-bottom: 2rem !important; }

.gap-9 { gap: 2.25rem !important; }

.z-index-5 { z-index: 5; }

/* Card Styles */
.card {
    border-radius: 10px;
    border: none;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.card-title {
    color: #2a3547;
    margin-bottom: 1rem;
}

/* Alert Styles */
.alert {
    border-radius: 8px;
    border: none;
}

.alert-success {
    background-color: #d1e7dd;
    color: #0f5132;
}

.alert-danger {
    background-color: #f8d7da;
    color: #842029;
}

/* Breadcrumb */
.breadcrumb-item + .breadcrumb-item::before {
    content: ">";
}

/* Navigation */
.nav-small-cap {
    padding: 15px 20px 5px;
    font-size: 11px;
    font-weight: 600;
    color: #a6b0cf;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.nav-small-cap-icon {
    margin-right: 8px;
}

/* Profile Dropdown */
.profile-dropdown {
    min-width: 280px;
}

.user-profile-img img {
    border: 2px solid #fff;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

/* Round indicators */
.round-8 {
    width: 8px;
    height: 8px;
}
