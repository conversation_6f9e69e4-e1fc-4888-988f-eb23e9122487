// Minified app scripts
!function(){"use strict";document.addEventListener("DOMContentLoaded",function(){const e=document.querySelector(".preloader");e&&setTimeout(()=>{e.style.display="none"},1e3);[].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]')).map(function(e){return new bootstrap.Tooltip(e)});[].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]')).map(function(e){return new bootstrap.Popover(e)})})}();
