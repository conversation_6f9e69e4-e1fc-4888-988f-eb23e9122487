<!DOCTYPE html>
<html lang="vi" dir="ltr" data-bs-theme="light" data-color-theme="Blue_Theme" data-layout="vertical">

<head>
  <!-- Required meta tags -->
  <meta charset="UTF-8" />
  <meta http-equiv="X-UA-Compatible" content="IE=edge" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />

  <!-- Favicon icon-->
  <link rel="shortcut icon" type="image/png" href="<?php echo e(asset('assets/images/logos/favicon.png')); ?>" />

  <!-- Bootstrap CSS -->
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
  <!-- Font Awesome Icons -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
  <!-- Core Css -->
  <link rel="stylesheet" href="<?php echo e(asset('assets/css/styles.css')); ?>" />

  <title>Đăng Nhập - Hệ Thống Quản Lý Khóa Học</title>
</head>

<body>
  <!-- Preloader -->
  <div class="preloader">
    <img src="<?php echo e(asset('assets/images/logos/favicon.png')); ?>" alt="loader" class="lds-ripple img-fluid" />
  </div>
  <div id="main-wrapper">
    <div class="position-relative overflow-hidden auth-bg min-vh-100 w-100 d-flex align-items-center justify-content-center">
      <div class="d-flex align-items-center justify-content-center w-100">
        <div class="row justify-content-center w-100 my-5 my-xl-0">
          <div class="col-md-9 d-flex flex-column justify-content-center">
            <div class="card mb-0 bg-body auth-login m-auto w-100">
              <div class="row gx-0">
                <!-- ------------------------------------------------- -->
                <!-- Phần 1 - Form Đăng Nhập -->
                <!-- ------------------------------------------------- -->
                <div class="col-xl-6 border-end">
                  <div class="row justify-content-center py-4">
                    <div class="col-lg-11">
                      <div class="card-body">
                        <a href="<?php echo e(route('home')); ?>" class="text-nowrap logo-img d-block mb-4 w-100">
                          <img src="<?php echo e(asset('assets/images/logos/logo.svg')); ?>" class="dark-logo" alt="Logo" />
                        </a>
                        <h2 class="lh-base mb-4">Chào Mừng Bạn Đăng Nhập</h2>
                        
                        <?php if($errors->any()): ?>
                            <div class="alert alert-danger">
                                <ul class="mb-0">
                                    <?php $__currentLoopData = $errors->all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $error): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <li><?php echo e($error); ?></li>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </ul>
                            </div>
                        <?php endif; ?>

                        <?php if(session('status')): ?>
                            <div class="alert alert-success">
                                <?php echo e(session('status')); ?>

                            </div>
                        <?php endif; ?>

                        <div class="row">
                          <div class="col-6 mb-2 mb-sm-0">
                            <a class="btn btn-white shadow-sm text-dark link-primary border fw-semibold d-flex align-items-center justify-content-center rounded-1 py-6" href="javascript:void(0)" role="button">
                              <i class="fa fa-facebook me-2" style="color: #1877f2;"></i>
                              <span class="d-none d-xxl-inline-flex">Đăng Nhập Với</span>&nbsp; Facebook
                            </a>
                          </div>
                          <div class="col-6">
                            <a class="btn btn-white shadow-sm text-dark link-primary border fw-semibold d-flex align-items-center justify-content-center rounded-1 py-6" href="javascript:void(0)" role="button">
                              <i class="fa fa-google me-2" style="color: #db4437;"></i>
                              <span class="d-none d-xxl-inline-flex">Đăng Nhập Với</span>&nbsp; Google
                            </a>
                          </div>
                        </div>
                        <div class="position-relative text-center my-4">
                          <p class="mb-0 fs-12 px-3 d-inline-block bg-body z-index-5 position-relative">Hoặc Đăng Nhập Bằng Email</p>
                          <span class="border-top w-100 position-absolute top-50 start-50 translate-middle"></span>
                        </div>
                        
                        <form method="POST" action="<?php echo e(route('login')); ?>">
                            <?php echo csrf_field(); ?>
                          <div class="mb-3">
                            <label for="email" class="form-label">Địa Chỉ Email</label>
                            <input type="email" class="form-control <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" id="email" name="email" value="<?php echo e(old('email')); ?>" placeholder="Nhập địa chỉ email của bạn" required autofocus>
                            <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                          </div>
                          <div class="mb-4">
                            <div class="d-flex align-items-center justify-content-between">
                              <label for="password" class="form-label">Mật Khẩu</label>
                              <a class="text-primary link-dark fs-2" href="<?php echo e(route('password.request')); ?>">Quên Mật Khẩu?</a>
                            </div>
                            <input type="password" class="form-control <?php $__errorArgs = ['password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" id="password" name="password" placeholder="Nhập mật khẩu của bạn" required>
                            <?php $__errorArgs = ['password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                          </div>
                          <div class="d-flex align-items-center justify-content-between mb-4">
                            <div class="form-check">
                              <input class="form-check-input primary" type="checkbox" name="remember" id="remember" <?php echo e(old('remember') ? 'checked' : ''); ?>>
                              <label class="form-check-label text-dark" for="remember">
                                Ghi Nhớ Đăng Nhập
                              </label>
                            </div>
                          </div>
                          <button type="submit" class="btn btn-dark w-100 py-8 mb-4 rounded-1">Đăng Nhập</button>
                          <div class="d-flex align-items-center">
                            <p class="fs-12 mb-0 fw-medium">Chưa Có Tài Khoản?</p>
                            <a class="text-primary fw-bolder ms-2" href="<?php echo e(route('register')); ?>">Đăng Ký Ngay</a>
                          </div>
                        </form>
                      </div>
                    </div>
                  </div>
                </div>
                <!-- ------------------------------------------------- -->
                <!-- Phần 2 - Hình Ảnh Minh Họa -->
                <!-- ------------------------------------------------- -->
                <div class="col-xl-6 d-none d-xl-block">
                  <div class="row justify-content-center align-items-start h-100">
                    <div class="col-lg-9">
                      <div id="auth-login" class="carousel slide auth-carousel mt-5 pt-4" data-bs-ride="carousel">
                        <div class="carousel-indicators">
                          <button type="button" data-bs-target="#auth-login" data-bs-slide-to="0" class="active" aria-current="true" aria-label="Slide 1"></button>
                          <button type="button" data-bs-target="#auth-login" data-bs-slide-to="1" aria-label="Slide 2"></button>
                          <button type="button" data-bs-target="#auth-login" data-bs-slide-to="2" aria-label="Slide 3"></button>
                        </div>
                        <div class="carousel-inner">
                          <div class="carousel-item active">
                            <div class="d-flex align-items-center justify-content-center w-100 h-100 flex-column gap-9 text-center">
                              <img src="<?php echo e(asset('assets/images/backgrounds/login-side.png')); ?>" alt="login-side-img" width="300" class="img-fluid" />
                              <h4 class="mb-0">Hệ Thống Quản Lý Khóa Học Hiện Đại</h4>
                              <p class="fs-12 mb-0">Nền tảng học tập trực tuyến với giao diện thân thiện và tính năng đa dạng, giúp bạn quản lý và theo dõi tiến độ học tập một cách hiệu quả.</p>
                              <a href="javascript:void(0)" class="btn btn-primary rounded-1">Tìm Hiểu Thêm</a>
                            </div>
                          </div>
                          <div class="carousel-item">
                            <div class="d-flex align-items-center justify-content-center w-100 h-100 flex-column gap-9 text-center">
                              <img src="<?php echo e(asset('assets/images/backgrounds/login-side.png')); ?>" alt="login-side-img" width="300" class="img-fluid" />
                              <h4 class="mb-0">Theo Dõi Tiến Độ Học Tập</h4>
                              <p class="fs-12 mb-0">Hệ thống báo cáo chi tiết giúp bạn theo dõi tiến độ học tập, đánh giá kết quả và cải thiện phương pháp học tập.</p>
                              <a href="javascript:void(0)" class="btn btn-primary rounded-1">Khám Phá Ngay</a>
                            </div>
                          </div>
                          <div class="carousel-item">
                            <div class="d-flex align-items-center justify-content-center w-100 h-100 flex-column gap-9 text-center">
                              <img src="<?php echo e(asset('assets/images/backgrounds/login-side.png')); ?>" alt="login-side-img" width="300" class="img-fluid" />
                              <h4 class="mb-0">Giao Diện Thân Thiện</h4>
                              <p class="fs-12 mb-0">Thiết kế giao diện đơn giản, dễ sử dụng với trải nghiệm người dùng tối ưu trên mọi thiết bị.</p>
                              <a href="javascript:void(0)" class="btn btn-primary rounded-1">Bắt Đầu Ngay</a>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="dark-transparent sidebartoggler"></div>
  <!-- Import Js Files -->
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
  <script src="<?php echo e(asset('assets/js/theme/app.init.js')); ?>"></script>
  <script src="<?php echo e(asset('assets/js/theme/theme.js')); ?>"></script>
  <script src="<?php echo e(asset('assets/js/theme/app.min.js')); ?>"></script>
</body>

</html>
<?php /**PATH D:\Src Công Việc\PanDa\resources\views/auth/login.blade.php ENDPATH**/ ?>