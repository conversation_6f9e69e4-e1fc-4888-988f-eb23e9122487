// Theme Configuration
(function() {
    'use strict';
    
    // Sidebar Toggle
    const sidebarToggler = document.querySelector('.sidebartoggler');
    const sidebar = document.querySelector('.left-sidebar');
    const pageWrapper = document.querySelector('.page-wrapper');
    const topbar = document.querySelector('.topbar');
    
    if (sidebarToggler) {
        sidebarToggler.addEventListener('click', function() {
            if (window.innerWidth > 1199) {
                // Desktop behavior
                if (sidebar.style.transform === 'translateX(-100%)') {
                    sidebar.style.transform = 'translateX(0)';
                    pageWrapper.style.marginLeft = '270px';
                    topbar.style.left = '270px';
                } else {
                    sidebar.style.transform = 'translateX(-100%)';
                    pageWrapper.style.marginLeft = '0';
                    topbar.style.left = '0';
                }
            } else {
                // Mobile behavior
                sidebar.classList.toggle('show-sidebar');
            }
        });
    }
    
    // Close sidebar on mobile when clicking outside
    document.addEventListener('click', function(e) {
        if (window.innerWidth <= 1199) {
            if (!sidebar.contains(e.target) && !sidebarToggler.contains(e.target)) {
                sidebar.classList.remove('show-sidebar');
            }
        }
    });
    
    // Handle window resize
    window.addEventListener('resize', function() {
        if (window.innerWidth > 1199) {
            sidebar.classList.remove('show-sidebar');
            sidebar.style.transform = 'translateX(0)';
            pageWrapper.style.marginLeft = '270px';
            topbar.style.left = '270px';
        } else {
            sidebar.style.transform = '';
            pageWrapper.style.marginLeft = '0';
            topbar.style.left = '0';
        }
    });
    
})();
