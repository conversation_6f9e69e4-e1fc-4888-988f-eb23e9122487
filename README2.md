giai đoạn 1 : <PERSON><PERSON><PERSON><PERSON> lý <PERSON> học (Subject):
* <PERSON><PERSON><PERSON>, chỉnh sửa, x<PERSON><PERSON> môn học.
* Thiết lập thông tin: tê<PERSON>, mô tả, g<PERSON><PERSON> gố<PERSON>, g<PERSON><PERSON> khu<PERSON>ến mãi.
* Tải lên hình ảnh đại diện môn học.
* Đặt trạng thái: <PERSON><PERSON><PERSON><PERSON> (Draft), <PERSON><PERSON><PERSON><PERSON> (Published), <PERSON><PERSON><PERSON> tr<PERSON> (Archived).
* Sắp xếp thứ tự môn học bằng kéo thả (drag & drop).
* Gán giảng viên cho từng môn học (hiển thị thông tin giảng viên cho học viên).
* Theo dõi tiến độ hoàn thành môn học của học viên.
giai đoạn 2 : Qu<PERSON>n lý Chương (Chapter):
* <PERSON><PERSON><PERSON>, chỉnh sửa, x<PERSON><PERSON> chương trong từng môn học.
* Đặt tên và mô tả chương.
* Sắp xếp thứ tự các chương.
* Thiết lập thời gian mở khóa chươ<PERSON> (nếu có).
giai đoạn 3 : Quản lý Bài học (Lesson):
* Tạo, chỉnh sửa, xóa bài học trong từng chương.
* Loại nội dung:
   * Video: Nhập link YouTube, tự động lấy thumbnail và thời lượng.
   * Văn bản: Trình chỉnh sửa văn bản giàu định dạng (rich text) hỗ trợ hình ảnh, bảng, khối mã (code block).
   * Tệp: Tải lên file PDF, Word, Excel, PowerPoint (giới hạn dung lượng).
* Đặt tên và mô tả bài học.
* Thiết lập thời lượng ước tính.
* Đánh dấu bài học miễn phí (xem trước).
* Sắp xếp thứ tự bài học.
* Thiết lập điều kiện mở khóa bài (phải hoàn thành bài trước).